import { Card, CardContent } from "@/components/ui/card";
import { Section, SectionHeader } from "@/components/ui/section";
import { 
  BookOpen, 
  FlaskConical, 
  Monitor, 
  Bus, 
  TreePine, 
  Utensils,
  Dumbbell,
  Music
} from "lucide-react";

const facilities = [
  {
    icon: BookOpen,
    title: "Library",
    description: "Well-stocked library with books, journals, and digital resources for all age groups."
  },
  {
    icon: FlaskConical,
    title: "Science Lab",
    description: "Modern laboratories for Physics, Chemistry, and Biology with latest equipment."
  },
  {
    icon: Monitor,
    title: "Smart Classes",
    description: "Interactive smart boards and digital learning tools in every classroom."
  },
  {
    icon: Bus,
    title: "Transportation",
    description: "Safe and reliable school bus service covering major areas of Biratnagar."
  },
  {
    icon: TreePine,
    title: "Playground",
    description: "Spacious outdoor playground and sports facilities for physical activities."
  },
  {
    icon: Utensils,
    title: "Cafeteria",
    description: "Hygienic cafeteria serving nutritious meals and healthy snacks."
  },
  {
    icon: <PERSON><PERSON><PERSON>,
    title: "Sports Complex",
    description: "Indoor sports facilities including basketball court and gymnasium."
  },
  {
    icon: Music,
    title: "Music Room",
    description: "Dedicated spaces for music, dance, and performing arts activities."
  }
];

export default function Facilities() {
  return (
    <Section>
      <SectionHeader
        title="Our Facilities"
        subtitle="Modern infrastructure and comprehensive facilities designed to provide the best learning environment for our students."
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {facilities.map((facility, index) => (
          <Card key={index} className="text-center hover:shadow-lg transition-shadow duration-300 border-l-4 border-l-blue-600">
            <CardContent className="p-6">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-50 rounded-full">
                  <facility.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900">
                {facility.title}
              </h3>
              <p className="text-sm text-gray-600">
                {facility.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </Section>
  );
}
