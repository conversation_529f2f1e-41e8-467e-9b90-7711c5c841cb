import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Section, SectionHeader } from "@/components/ui/section";
import { Quote } from "lucide-react";

const testimonials = [
  {
    name: "Mrs. <PERSON><PERSON>",
    role: "Parent of Grade 5 Student",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
    content: "Bright Future School has been wonderful for my daughter. The teachers are caring and the facilities are excellent. I'm impressed with how much she has grown academically and personally."
  },
  {
    name: "Mr. <PERSON><PERSON>",
    role: "Parent of Grade 8 Student",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    content: "The school's focus on both academics and extracurricular activities is commendable. My son has developed confidence and leadership skills that will serve him well in the future."
  },
  {
    name: "Priya Rai",
    role: "Grade 10 Student",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    content: "I love studying at Bright Future School. The teachers make learning fun and interesting. The science lab and library are my favorite places. I feel prepared for my future studies."
  }
];

export default function Testimonials() {
  return (
    <Section>
      <SectionHeader
        title="What Our Community Says"
        subtitle="Hear from our students and parents about their experience at Bright Future School."
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <Card key={index} className="relative hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <Quote className="h-8 w-8 text-blue-600 mb-4" />
              <p className="text-gray-600 mb-6 italic">
                "{testimonial.content}"
              </p>
              <div className="flex items-center">
                <Avatar className="h-12 w-12 mr-4">
                  <AvatarImage src={testimonial.image} alt={testimonial.name} />
                  <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </Section>
  );
}
