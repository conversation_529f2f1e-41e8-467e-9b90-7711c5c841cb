import { Card, CardContent } from "@/components/ui/card";
import { Section, SectionHeader } from "@/components/ui/section";
import { 
  TreePine, 
  Wifi, 
  Shield, 
  Camera, 
  Car, 
  Utensils,
  BookOpen,
  FlaskConical,
  Monitor,
  Dumbbell
} from "lucide-react";

const environmentFeatures = [
  {
    icon: TreePine,
    title: "Green Campus",
    description: "Eco-friendly environment with lush gardens and tree-lined pathways"
  },
  {
    icon: Shield,
    title: "Safe & Secure",
    description: "24/7 security with trained guards and secure campus boundaries"
  },
  {
    icon: Camera,
    title: "CCTV Monitoring",
    description: "Complete campus surveillance for student safety and security"
  },
  {
    icon: Wifi,
    title: "High-Speed Internet",
    description: "Campus-wide WiFi connectivity for digital learning resources"
  }
];

const facilities = [
  {
    icon: Monitor,
    title: "Smart Classrooms",
    description: "Interactive whiteboards and multimedia learning tools"
  },
  {
    icon: FlaskConical,
    title: "Science Laboratories",
    description: "Well-equipped Physics, Chemistry, and Biology labs"
  },
  {
    icon: BookOpen,
    title: "Digital Library",
    description: "Extensive collection of books and digital resources"
  },
  {
    icon: Du<PERSON><PERSON>,
    title: "Sports Complex",
    description: "Indoor and outdoor sports facilities for all activities"
  },
  {
    icon: Car,
    title: "Transportation",
    description: "Safe and reliable school bus service"
  },
  {
    icon: Utensils,
    title: "Cafeteria",
    description: "Hygienic food service with nutritious meal options"
  }
];

export default function EnvironmentFacilities() {
  return (
    <Section>
      <SectionHeader
        title="Learning Environment & Facilities"
        subtitle="A nurturing environment with world-class facilities designed for optimal learning and development"
      />
      
      {/* Environment Section */}
      <div className="mb-16">
        <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">Campus Environment</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {environmentFeatures.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-green-100 rounded-full">
                    <feature.icon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <h4 className="text-lg font-semibold mb-2 text-gray-900">
                  {feature.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Facilities Section */}
      <div>
        <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">Modern Facilities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {facilities.map((facility, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-300 border-l-4 border-l-blue-600">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <div className="p-3 bg-blue-50 rounded-full mr-4 flex-shrink-0">
                    <facility.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold mb-2 text-gray-900">
                      {facility.title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {facility.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Campus Stats */}
      <div className="mt-16 bg-blue-50 rounded-lg p-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div>
            <h4 className="text-3xl font-bold text-blue-600 mb-2">5</h4>
            <p className="text-gray-700">Acres Campus</p>
          </div>
          <div>
            <h4 className="text-3xl font-bold text-blue-600 mb-2">50+</h4>
            <p className="text-gray-700">Classrooms</p>
          </div>
          <div>
            <h4 className="text-3xl font-bold text-blue-600 mb-2">1200+</h4>
            <p className="text-gray-700">Students</p>
          </div>
          <div>
            <h4 className="text-3xl font-bold text-blue-600 mb-2">80+</h4>
            <p className="text-gray-700">Faculty Members</p>
          </div>
        </div>
      </div>
    </Section>
  );
}
