import { <PERSON><PERSON> } from "@/components/ui/button";
import { Section } from "@/components/ui/section";
import { Phone, MapPin, Clock } from "lucide-react";
import Link from "next/link";

export default function ContactCTA() {
  return (
    <Section className="bg-blue-600 text-white">
      <div className="text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">
          Ready to Join Our School Family?
        </h2>
        <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
          Take the first step towards your child's bright future. Contact us today to learn more 
          about admissions or schedule a campus visit.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
          <div className="flex flex-col items-center">
            <Phone className="h-8 w-8 mb-3 text-yellow-400" />
            <h3 className="font-semibold mb-2">Call Us</h3>
            <p className="text-blue-100">+977-21-123456</p>
          </div>
          <div className="flex flex-col items-center">
            <MapPin className="h-8 w-8 mb-3 text-yellow-400" />
            <h3 className="font-semibold mb-2">Visit Us</h3>
            <p className="text-blue-100">Main Road, Biratnagar-10</p>
          </div>
          <div className="flex flex-col items-center">
            <Clock className="h-8 w-8 mb-3 text-yellow-400" />
            <h3 className="font-semibold mb-2">Office Hours</h3>
            <p className="text-blue-100">Sun-Fri: 7:00 AM - 4:00 PM</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
            <Link href="/contact">Contact Us</Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600">
            <Link href="/admissions">View Admissions</Link>
          </Button>
        </div>
      </div>
    </Section>
  );
}
