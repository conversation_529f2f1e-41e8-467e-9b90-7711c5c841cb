{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\nimport Link from \"next/link\";\n\nexport default function HeroSection() {\n  return (\n    <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-r from-blue-600 to-blue-800\">\n      {/* Background Image Overlay */}\n      <div className=\"absolute inset-0 bg-black opacity-40\"></div>\n      \n      {/* Background Image (placeholder) */}\n      <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\" \n           style={{\n             backgroundImage: \"url('https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')\"\n           }}>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto\">\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n          Welcome to <span className=\"text-yellow-400\">Bright Future School</span>\n        </h1>\n        <p className=\"text-xl md:text-2xl mb-8 text-gray-200\">\n          Nurturing Young Minds, Building Bright Futures in Biratnagar, Nepal\n        </p>\n        <p className=\"text-lg mb-10 text-gray-300 max-w-2xl mx-auto\">\n          Quality education from pre-primary to secondary level with modern facilities, \n          experienced teachers, and a commitment to excellence in learning.\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button asChild size=\"lg\" className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\">\n            <Link href=\"/about\">Explore Our School</Link>\n          </Button>\n          <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white hover:text-blue-800\">\n            <Link href=\"/admissions\">Apply Now</Link>\n          </Button>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white rounded-full mt-2\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;;;;;;0BAIL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAsC;0CACvC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAE/C,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;kCAI7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;gCAAK,WAAU;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAS;;;;;;;;;;;0CAEtB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/ui/section.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface SectionProps {\n  children: React.ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Section({ children, className, id }: SectionProps) {\n  return (\n    <section\n      id={id}\n      className={cn(\"py-16 px-4 sm:px-6 lg:px-8\", className)}\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {children}\n      </div>\n    </section>\n  );\n}\n\ninterface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  className?: string;\n}\n\nexport function SectionHeader({ title, subtitle, className }: SectionHeaderProps) {\n  return (\n    <div className={cn(\"text-center mb-12\", className)}>\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n        {title}\n      </h2>\n      {subtitle && (\n        <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n          {subtitle}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAQO,SAAS,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAgB;IAC/D,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAE5C,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAQO,SAAS,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAsB;IAC9E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;;0BACtC,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport Link from \"next/link\";\n\nexport default function AboutPreview() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n        {/* Image */}\n        <div className=\"order-2 lg:order-1\">\n          <div className=\"relative h-96 rounded-lg overflow-hidden shadow-lg\">\n            <img\n              src=\"https://images.unsplash.com/photo-1580582932707-520aed937b7b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2032&q=80\"\n              alt=\"School Building\"\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"order-1 lg:order-2\">\n          <SectionHeader\n            title=\"About Bright Future School\"\n            subtitle=\"\"\n            className=\"text-left mb-8\"\n          />\n          <div className=\"space-y-4 text-gray-600\">\n            <p className=\"text-lg\">\n              Established in 2010, Bright Future School has been a beacon of quality education \n              in Biratnagar, Nepal. We are committed to providing holistic education that \n              nurtures not just academic excellence but also character development.\n            </p>\n            <p>\n              Our modern campus features state-of-the-art facilities including smart classrooms, \n              well-equipped laboratories, a comprehensive library, and spacious playgrounds. \n              We believe in creating an environment where every child can thrive and reach \n              their full potential.\n            </p>\n            <p>\n              With a team of dedicated and experienced teachers, we follow a student-centered \n              approach that encourages critical thinking, creativity, and lifelong learning. \n              Our curriculum is designed to meet international standards while staying rooted \n              in our cultural values.\n            </p>\n          </div>\n          <div className=\"mt-8\">\n            <Button asChild>\n              <Link href=\"/about\">Learn More About Us</Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,OAAM;4BACN,UAAS;4BACT,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAKvB,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { GraduationCap, Heart, Shield, Trophy } from \"lucide-react\";\n\nconst features = [\n  {\n    icon: GraduationCap,\n    title: \"Qualified Teachers\",\n    description: \"Our experienced and certified teachers are passionate about education and committed to student success.\"\n  },\n  {\n    icon: Heart,\n    title: \"Child-Centered Learning\",\n    description: \"We focus on individual learning styles and provide personalized attention to help every child excel.\"\n  },\n  {\n    icon: Shield,\n    title: \"Safe Environment\",\n    description: \"A secure and nurturing environment where children feel safe to learn, explore, and grow.\"\n  },\n  {\n    icon: Trophy,\n    title: \"Sports & Arts\",\n    description: \"Comprehensive programs in sports, music, dance, and arts to develop well-rounded personalities.\"\n  }\n];\n\nexport default function WhyChooseUs() {\n  return (\n    <Section>\n      <SectionHeader\n        title=\"Why Choose Bright Future School?\"\n        subtitle=\"We provide quality education with a focus on holistic development, ensuring every child reaches their full potential.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n        {features.map((feature, index) => (\n          <Card key={index} className=\"text-center hover:shadow-lg transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"p-3 bg-blue-100 rounded-full\">\n                  <feature.icon className=\"h-8 w-8 text-blue-600\" />\n                </div>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-3 text-gray-900\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {feature.description}\n              </p>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;kCAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG5B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;;;;;;uBAXf;;;;;;;;;;;;;;;;AAmBrB", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from \"@/components/ui/card\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { Baby, BookOpen, Users } from \"lucide-react\";\n\nconst programs = [\n  {\n    icon: Baby,\n    title: \"Pre-Primary\",\n    subtitle: \"Nursery, LKG, UKG\",\n    description: \"A nurturing environment for early childhood development with play-based learning, basic literacy, and social skills development.\",\n    features: [\"Play-based learning\", \"Basic literacy & numeracy\", \"Social skills\", \"Creative activities\"]\n  },\n  {\n    icon: BookOpen,\n    title: \"Primary\",\n    subtitle: \"Grades 1-5\",\n    description: \"Building strong foundations in core subjects while encouraging curiosity, critical thinking, and love for learning.\",\n    features: [\"Core subjects\", \"English & Nepali\", \"Mathematics & Science\", \"Computer basics\"]\n  },\n  {\n    icon: Users,\n    title: \"Secondary\",\n    subtitle: \"Grades 6-10\",\n    description: \"Comprehensive education preparing students for higher studies with advanced subjects and career guidance.\",\n    features: [\"Advanced curriculum\", \"Science & Commerce\", \"Career counseling\", \"Exam preparation\"]\n  }\n];\n\nexport default function AcademicPrograms() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <SectionHeader\n        title=\"Academic Programs\"\n        subtitle=\"Comprehensive education from early childhood to secondary level, designed to nurture young minds and prepare them for future success.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        {programs.map((program, index) => (\n          <Card key={index} className=\"hover:shadow-lg transition-shadow duration-300\">\n            <CardHeader className=\"text-center\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"p-4 bg-blue-100 rounded-full\">\n                  <program.icon className=\"h-10 w-10 text-blue-600\" />\n                </div>\n              </div>\n              <CardTitle className=\"text-2xl text-gray-900\">{program.title}</CardTitle>\n              <p className=\"text-blue-600 font-medium\">{program.subtitle}</p>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-600 mb-4\">{program.description}</p>\n              <ul className=\"space-y-2\">\n                {program.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-center text-sm text-gray-700\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    {feature}\n                  </li>\n                ))}\n              </ul>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAuB;YAA6B;YAAiB;SAAsB;IACxG;IACA;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAiB;YAAoB;YAAyB;SAAkB;IAC7F;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAuB;YAAsB;YAAqB;SAAmB;IAClG;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;;0CAC1B,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA0B,QAAQ,KAAK;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAA6B,QAAQ,QAAQ;;;;;;;;;;;;0CAE5D,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAE,WAAU;kDAAsB,QAAQ,WAAW;;;;;;kDACtD,8OAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;wDAAI,WAAU;;;;;;oDACd;;+CAFM;;;;;;;;;;;;;;;;;uBAdN;;;;;;;;;;;;;;;;AA0BrB", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { \n  BookOpen, \n  FlaskConical, \n  Monitor, \n  Bus, \n  TreePine, \n  Utensils,\n  Dumbbell,\n  Music\n} from \"lucide-react\";\n\nconst facilities = [\n  {\n    icon: BookOpen,\n    title: \"Library\",\n    description: \"Well-stocked library with books, journals, and digital resources for all age groups.\"\n  },\n  {\n    icon: FlaskConical,\n    title: \"Science Lab\",\n    description: \"Modern laboratories for Physics, Chemistry, and Biology with latest equipment.\"\n  },\n  {\n    icon: Monitor,\n    title: \"Smart Classes\",\n    description: \"Interactive smart boards and digital learning tools in every classroom.\"\n  },\n  {\n    icon: Bus,\n    title: \"Transportation\",\n    description: \"Safe and reliable school bus service covering major areas of Biratnagar.\"\n  },\n  {\n    icon: TreePine,\n    title: \"Playground\",\n    description: \"Spacious outdoor playground and sports facilities for physical activities.\"\n  },\n  {\n    icon: Utensils,\n    title: \"Cafeteria\",\n    description: \"Hygienic cafeteria serving nutritious meals and healthy snacks.\"\n  },\n  {\n    icon: <PERSON><PERSON><PERSON>,\n    title: \"Sports Complex\",\n    description: \"Indoor sports facilities including basketball court and gymnasium.\"\n  },\n  {\n    icon: Music,\n    title: \"Music Room\",\n    description: \"Dedicated spaces for music, dance, and performing arts activities.\"\n  }\n];\n\nexport default function Facilities() {\n  return (\n    <Section>\n      <SectionHeader\n        title=\"Our Facilities\"\n        subtitle=\"Modern infrastructure and comprehensive facilities designed to provide the best learning environment for our students.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {facilities.map((facility, index) => (\n          <Card key={index} className=\"text-center hover:shadow-lg transition-shadow duration-300 border-l-4 border-l-blue-600\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"p-3 bg-blue-50 rounded-full\">\n                  <facility.icon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <h3 className=\"text-lg font-semibold mb-2 text-gray-900\">\n                {facility.title}\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                {facility.description}\n              </p>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAWA,MAAM,aAAa;IACjB;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sNAAA,CAAA,eAAY;QAClB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;kCAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,SAAS,IAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG7B,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAE,WAAU;8CACV,SAAS,WAAW;;;;;;;;;;;;uBAXhB;;;;;;;;;;;;;;;;AAmBrB", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport Link from \"next/link\";\n\nconst galleryImages = [\n  {\n    src: \"https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Students in classroom\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2032&q=80\",\n    alt: \"Science laboratory\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Students playing sports\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80\",\n    alt: \"Library reading area\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2022&q=80\",\n    alt: \"Art and craft activities\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Annual day celebration\"\n  }\n];\n\nexport default function GalleryPreview() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <SectionHeader\n        title=\"School Life Gallery\"\n        subtitle=\"Take a glimpse into our vibrant school community and the wonderful moments we create together.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n        {galleryImages.map((image, index) => (\n          <div key={index} className=\"relative group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\">\n            <img\n              src={image.src}\n              alt={image.alt}\n              className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-center justify-center\">\n              <span className=\"text-white font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                {image.alt}\n              </span>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"text-center\">\n        <Button asChild size=\"lg\">\n          <Link href=\"/gallery\">View Full Gallery</Link>\n        </Button>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB;IACpB;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCACC,KAAK,MAAM,GAAG;gCACd,KAAK,MAAM,GAAG;gCACd,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,MAAM,GAAG;;;;;;;;;;;;uBARN;;;;;;;;;;0BAed,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,MAAK;8BACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAW;;;;;;;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { Quote } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    name: \"Mrs. <PERSON><PERSON>\",\n    role: \"Parent of Grade 5 Student\",\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n    content: \"Bright Future School has been wonderful for my daughter. The teachers are caring and the facilities are excellent. I'm impressed with how much she has grown academically and personally.\"\n  },\n  {\n    name: \"Mr. <PERSON><PERSON>\",\n    role: \"Parent of Grade 8 Student\",\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n    content: \"The school's focus on both academics and extracurricular activities is commendable. My son has developed confidence and leadership skills that will serve him well in the future.\"\n  },\n  {\n    name: \"Priya Rai\",\n    role: \"Grade 10 Student\",\n    image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n    content: \"I love studying at Bright Future School. The teachers make learning fun and interesting. The science lab and library are my favorite places. I feel prepared for my future studies.\"\n  }\n];\n\nexport default function Testimonials() {\n  return (\n    <Section>\n      <SectionHeader\n        title=\"What Our Community Says\"\n        subtitle=\"Hear from our students and parents about their experience at Bright Future School.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        {testimonials.map((testimonial, index) => (\n          <Card key={index} className=\"relative hover:shadow-lg transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              <Quote className=\"h-8 w-8 text-blue-600 mb-4\" />\n              <p className=\"text-gray-600 mb-6 italic\">\n                \"{testimonial.content}\"\n              </p>\n              <div className=\"flex items-center\">\n                <Avatar className=\"h-12 w-12 mr-4\">\n                  <AvatarImage src={testimonial.image} alt={testimonial.name} />\n                  <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>\n                </Avatar>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                  <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;kCAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;;wCAA4B;wCACrC,YAAY,OAAO;wCAAC;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,YAAY,KAAK;oDAAE,KAAK,YAAY,IAAI;;;;;;8DAC1D,8OAAC,kIAAA,CAAA,iBAAc;8DAAE,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sDAEnE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC7D,8OAAC;oDAAE,WAAU;8DAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;uBAbnD;;;;;;;;;;;;;;;;AAsBrB", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/components/home/<USER>"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Section } from \"@/components/ui/section\";\nimport { Phone, MapPin, Clock } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function ContactCTA() {\n  return (\n    <Section className=\"bg-blue-600 text-white\">\n      <div className=\"text-center\">\n        <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n          Ready to Join Our School Family?\n        </h2>\n        <p className=\"text-xl mb-8 text-blue-100 max-w-2xl mx-auto\">\n          Take the first step towards your child's bright future. Contact us today to learn more \n          about admissions or schedule a campus visit.\n        </p>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-10\">\n          <div className=\"flex flex-col items-center\">\n            <Phone className=\"h-8 w-8 mb-3 text-yellow-400\" />\n            <h3 className=\"font-semibold mb-2\">Call Us</h3>\n            <p className=\"text-blue-100\">+977-21-123456</p>\n          </div>\n          <div className=\"flex flex-col items-center\">\n            <MapPin className=\"h-8 w-8 mb-3 text-yellow-400\" />\n            <h3 className=\"font-semibold mb-2\">Visit Us</h3>\n            <p className=\"text-blue-100\">Main Road, Biratnagar-10</p>\n          </div>\n          <div className=\"flex flex-col items-center\">\n            <Clock className=\"h-8 w-8 mb-3 text-yellow-400\" />\n            <h3 className=\"font-semibold mb-2\">Office Hours</h3>\n            <p className=\"text-blue-100\">Sun-Fri: 7:00 AM - 4:00 PM</p>\n          </div>\n        </div>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button asChild size=\"lg\" className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\">\n            <Link href=\"/contact\">Contact Us</Link>\n          </Button>\n          <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white hover:text-blue-600\">\n            <Link href=\"/admissions\">View Admissions</Link>\n          </Button>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAsC;;;;;;8BAGpD,8OAAC;oBAAE,WAAU;8BAA+C;;;;;;8BAK5D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,MAAK;4BAAK,WAAU;sCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAW;;;;;;;;;;;sCAExB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/school/school-website/src/app/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/home/<USER>\";\nimport AboutPreview from \"@/components/home/<USER>\";\nimport WhyChooseUs from \"@/components/home/<USER>\";\nimport AcademicPrograms from \"@/components/home/<USER>\";\nimport Facilities from \"@/components/home/<USER>\";\nimport GalleryPreview from \"@/components/home/<USER>\";\nimport Testimonials from \"@/components/home/<USER>\";\nimport ContactCTA from \"@/components/home/<USER>\";\n\nexport default function Home() {\n  return (\n    <div>\n      <HeroSection />\n      <AboutPreview />\n      <WhyChooseUs />\n      <AcademicPrograms />\n      <Facilities />\n      <GalleryPreview />\n      <Testimonials />\n      <ContactCTA />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,6IAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8IAAA,CAAA,UAAY;;;;;0BACb,8OAAC,iJAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,kJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,wIAAA,CAAA,UAAU;;;;;0BACX,8OAAC,gJAAA,CAAA,UAAc;;;;;0BACf,8OAAC,0IAAA,CAAA,UAAY;;;;;0BACb,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}